using AirMonitor.Models;

namespace AirMonitor.Services;

/// <summary>
/// 配置服务接口
/// </summary>
public interface IConfigurationService
{
    /// <summary>
    /// 获取应用程序配置
    /// </summary>
    AppSettings AppSettings { get; }

    /// <summary>
    /// 加载配置
    /// </summary>
    /// <returns>是否加载成功</returns>
    Task<bool> LoadConfigurationAsync();

    /// <summary>
    /// 保存配置
    /// </summary>
    /// <returns>是否保存成功</returns>
    Task<bool> SaveConfigurationAsync();

    /// <summary>
    /// 重新加载配置
    /// </summary>
    /// <returns>是否重新加载成功</returns>
    Task<bool> ReloadConfigurationAsync();

    /// <summary>
    /// 获取配置值
    /// </summary>
    /// <typeparam name="T">配置值类型</typeparam>
    /// <param name="key">配置键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>配置值</returns>
    T GetValue<T>(string key, T defaultValue) where T : notnull;

    /// <summary>
    /// 设置配置值
    /// </summary>
    /// <typeparam name="T">配置值类型</typeparam>
    /// <param name="key">配置键</param>
    /// <param name="value">配置值</param>
    void SetValue<T>(string key, T value) where T : notnull;

    /// <summary>
    /// 配置更改事件
    /// </summary>
    event EventHandler<ConfigurationChangedEventArgs>? ConfigurationChanged;
}

/// <summary>
/// 配置更改事件参数
/// </summary>
public class ConfigurationChangedEventArgs : EventArgs
{
    /// <summary>
    /// 更改的配置键
    /// </summary>
    public string Key { get; }

    /// <summary>
    /// 旧值
    /// </summary>
    public object? OldValue { get; }

    /// <summary>
    /// 新值
    /// </summary>
    public object? NewValue { get; }

    public ConfigurationChangedEventArgs(string key, object? oldValue, object? newValue)
    {
        Key = key;
        OldValue = oldValue;
        NewValue = newValue;
    }
}
