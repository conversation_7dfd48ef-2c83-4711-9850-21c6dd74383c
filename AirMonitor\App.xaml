﻿<Application
    x:Class="AirMonitor.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:AirMonitor.Converters"
    xmlns:local="clr-namespace:AirMonitor"
    StartupUri="MainWindow.xaml">
    <Application.Resources>
        <converters:BooleanToBrushConverter x:Key="BooleanToBrushConverter" />
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter" />
        <converters:NullToBooleanConverter x:Key="NullToBooleanConverter" />
        <converters:StringEmptyToBooleanConverter x:Key="StringEmptyToBooleanConverter" />
    </Application.Resources>
</Application>
