<UserControl x:Class="AirMonitor.Controls.ModalOverlay"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d" 
             d:DesignHeight="450" 
             d:DesignWidth="800"
             Background="Transparent"
             IsHitTestVisible="True">
    
    <UserControl.Resources>
        <!-- 淡入动画 -->
        <Storyboard x:Key="FadeInStoryboard">
            <DoubleAnimation Storyboard.TargetName="OverlayBorder"
                           Storyboard.TargetProperty="Opacity"
                           From="0" To="1"
                           Duration="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <QuadraticEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>
        
        <!-- 淡出动画 -->
        <Storyboard x:Key="FadeOutStoryboard">
            <DoubleAnimation Storyboard.TargetName="OverlayBorder"
                           Storyboard.TargetProperty="Opacity"
                           From="1" To="0"
                           Duration="0:0:0.15">
                <DoubleAnimation.EasingFunction>
                    <QuadraticEase EasingMode="EaseIn"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>
    </UserControl.Resources>
    
    <Border x:Name="OverlayBorder"
            Background="{Binding OverlayBrush, RelativeSource={RelativeSource AncestorType=UserControl}}"
            Opacity="0">
        
        <!-- 内容容器 -->
        <Grid x:Name="ContentGrid">
            <ContentPresenter x:Name="DialogContentPresenter"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Content="{Binding DialogContent, RelativeSource={RelativeSource AncestorType=UserControl}}"/>
        </Grid>
    </Border>
</UserControl>
