using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog.Extensions.Logging;
using AirMonitor.ViewModels;

namespace AirMonitor.Services;

/// <summary>
/// 服务容器，负责依赖注入的配置和管理
/// </summary>
public static class ServiceContainer
{
    private static IServiceProvider? _serviceProvider;
    private static readonly object _lock = new();

    /// <summary>
    /// 服务提供者
    /// </summary>
    public static IServiceProvider ServiceProvider
    {
        get
        {
            if (_serviceProvider == null)
            {
                throw new InvalidOperationException("服务容器尚未初始化，请先调用 Initialize 方法");
            }
            return _serviceProvider;
        }
    }

    /// <summary>
    /// 初始化服务容器
    /// </summary>
    /// <param name="configureServices">服务配置委托</param>
    public static void Initialize(Action<IServiceCollection>? configureServices = null)
    {
        lock (_lock)
        {
            if (_serviceProvider != null)
            {
                return; // 已经初始化
            }

            var services = new ServiceCollection();

            // 注册核心服务
            RegisterCoreServices(services);

            // 注册ViewModels
            RegisterViewModels(services);

            // 允许外部配置额外的服务
            configureServices?.Invoke(services);

            _serviceProvider = services.BuildServiceProvider();
        }
    }

    /// <summary>
    /// 注册核心服务
    /// </summary>
    /// <param name="services">服务集合</param>
    private static void RegisterCoreServices(IServiceCollection services)
    {
        // 注册日志服务
        services.AddLogging(builder =>
        {
            builder.ClearProviders();
            builder.AddProvider(new SerilogLoggerProvider());
        });

        // 注册配置服务
        services.AddSingleton<IConfigurationService, ConfigurationService>();

        // 注册日志服务
        services.AddSingleton<ILoggingService, LoggingService>();

        // 注册对话框服务
        services.AddSingleton<IDialogService, DialogService>();

        // 注册其他核心服务
        // services.AddSingleton<IOtherService, OtherService>();
    }

    /// <summary>
    /// 注册ViewModels
    /// </summary>
    /// <param name="services">服务集合</param>
    private static void RegisterViewModels(IServiceCollection services)
    {
        // 注册主窗口ViewModel
        services.AddTransient<MainViewModel>();

        // 注册示例ViewModel
        services.AddTransient<SampleViewModel>();

        // 注册示例对话框ViewModel
        services.AddTransient<SampleDialogViewModel>();

        // 注册其他ViewModels
        // services.AddTransient<OtherViewModel>();
    }

    /// <summary>
    /// 获取服务
    /// </summary>
    /// <typeparam name="T">服务类型</typeparam>
    /// <returns>服务实例</returns>
    public static T GetService<T>() where T : notnull
    {
        return ServiceProvider.GetRequiredService<T>();
    }

    /// <summary>
    /// 获取服务（可为空）
    /// </summary>
    /// <typeparam name="T">服务类型</typeparam>
    /// <returns>服务实例或null</returns>
    public static T? GetServiceOrNull<T>() where T : class
    {
        return ServiceProvider.GetService<T>();
    }

    /// <summary>
    /// 获取服务
    /// </summary>
    /// <param name="serviceType">服务类型</param>
    /// <returns>服务实例</returns>
    public static object GetService(Type serviceType)
    {
        return ServiceProvider.GetRequiredService(serviceType);
    }

    /// <summary>
    /// 获取服务（可为空）
    /// </summary>
    /// <param name="serviceType">服务类型</param>
    /// <returns>服务实例或null</returns>
    public static object? GetServiceOrNull(Type serviceType)
    {
        return ServiceProvider.GetService(serviceType);
    }

    /// <summary>
    /// 创建服务范围
    /// </summary>
    /// <returns>服务范围</returns>
    public static IServiceScope CreateScope()
    {
        return ServiceProvider.CreateScope();
    }

    /// <summary>
    /// 释放服务容器
    /// </summary>
    public static void Dispose()
    {
        lock (_lock)
        {
            if (_serviceProvider is IDisposable disposable)
            {
                disposable.Dispose();
            }
            _serviceProvider = null;
        }
    }
}

/// <summary>
/// 服务定位器（Service Locator）模式的实现
/// 注意：这是一个反模式，应该尽量避免使用，优先使用构造函数注入
/// </summary>
public static class ServiceLocator
{
    /// <summary>
    /// 获取服务
    /// </summary>
    /// <typeparam name="T">服务类型</typeparam>
    /// <returns>服务实例</returns>
    [Obsolete("请优先使用构造函数注入，避免使用服务定位器模式")]
    public static T GetService<T>() where T : notnull
    {
        return ServiceContainer.GetService<T>();
    }

    /// <summary>
    /// 获取服务（可为空）
    /// </summary>
    /// <typeparam name="T">服务类型</typeparam>
    /// <returns>服务实例或null</returns>
    [Obsolete("请优先使用构造函数注入，避免使用服务定位器模式")]
    public static T? GetServiceOrNull<T>() where T : class
    {
        return ServiceContainer.GetServiceOrNull<T>();
    }
}
