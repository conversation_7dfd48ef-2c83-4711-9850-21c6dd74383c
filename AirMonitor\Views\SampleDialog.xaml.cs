using AirMonitor.ViewModels;

namespace AirMonitor.Views;

/// <summary>
/// 示例对话框窗口
/// </summary>
public partial class SampleDialog : DialogWindowBase
{
    #region 构造函数

    /// <summary>
    /// 初始化 SampleDialog 类的新实例
    /// </summary>
    public SampleDialog()
    {
        InitializeComponent();
        DataContext = new SampleDialogViewModel();
    }

    /// <summary>
    /// 初始化 SampleDialog 类的新实例
    /// </summary>
    /// <param name="viewModel">视图模型</param>
    public SampleDialog(SampleDialogViewModel viewModel)
    {
        InitializeComponent();
        DataContext = viewModel ?? throw new ArgumentNullException(nameof(viewModel));
    }

    #endregion

    #region 重写方法

    /// <summary>
    /// 验证对话框数据
    /// </summary>
    protected override bool ValidateData()
    {
        if (DataContext is SampleDialogViewModel viewModel)
        {
            // 触发验证
            viewModel.ValidateAllProperties();
            
            // 检查是否有错误
            if (viewModel.HasErrors)
            {
                return false;
            }

            // 检查必填项
            if (!viewModel.IsAgreed)
            {
                System.Windows.MessageBox.Show("请同意相关条款和条件", "验证错误", 
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        return base.ValidateData();
    }

    /// <summary>
    /// 确定按钮点击事件处理
    /// </summary>
    protected override void OkButton_Click(object sender, System.Windows.RoutedEventArgs e)
    {
        if (ValidateData())
        {
            base.OkButton_Click(sender, e);
        }
    }

    #endregion

    #region 公共方法

    /// <summary>
    /// 获取对话框结果数据
    /// </summary>
    /// <returns>表单数据字典</returns>
    public Dictionary<string, object>? GetResultData()
    {
        if (DialogResult == true && DataContext is SampleDialogViewModel viewModel)
        {
            return viewModel.GetFormData();
        }
        return null;
    }

    /// <summary>
    /// 设置初始数据
    /// </summary>
    /// <param name="data">初始数据字典</param>
    public void SetInitialData(Dictionary<string, object> data)
    {
        if (DataContext is SampleDialogViewModel viewModel)
        {
            viewModel.SetFormData(data);
        }
    }

    #endregion
}
