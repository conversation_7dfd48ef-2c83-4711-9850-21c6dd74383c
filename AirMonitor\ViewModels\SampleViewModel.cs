using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using AirMonitor.Services;

namespace AirMonitor.ViewModels;

/// <summary>
/// 示例ViewModel，演示ViewModelLocator自动绑定功能
/// </summary>
public partial class SampleViewModel : ViewModelBase
{
    [ObservableProperty]
    private string _message = "这是一个示例用户控件，演示ViewModelLocator自动绑定功能";

    /// <summary>
    /// 测试命令
    /// </summary>
    public ICommand TestCommand { get; }

    public SampleViewModel()
    {
        Title = "示例ViewModel";
        TestCommand = new RelayCommand(ExecuteTestCommand);
    }

    public SampleViewModel(ILoggingService? loggingService) : base(loggingService!)
    {
        Title = "示例ViewModel";
        TestCommand = new RelayCommand(ExecuteTestCommand);
    }

    /// <summary>
    /// 执行测试命令
    /// </summary>
    private void ExecuteTestCommand()
    {
        LoggingService?.LogInformation("执行示例测试命令");
        
        Message = $"测试命令已执行 - {DateTime.Now:HH:mm:ss}";
        
        System.Windows.MessageBox.Show(
            "示例命令执行成功！\n\n这证明了ViewModelLocator自动绑定功能正常工作。",
            "测试成功",
            System.Windows.MessageBoxButton.OK,
            System.Windows.MessageBoxImage.Information);
    }
}
