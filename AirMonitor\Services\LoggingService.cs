using System.Diagnostics;
using Serilog;
using Serilog.Core;

namespace AirMonitor.Services;

/// <summary>
/// 日志服务实现
/// </summary>
public class LoggingService : ILoggingService, IDisposable
{
    private readonly ILogger _logger;
    private bool _disposed = false;

    public LoggingService()
    {
        _logger = Log.Logger;
    }

    public LoggingService(ILogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 记录详细信息
    /// </summary>
    public void LogVerbose(string message, params object[] args)
    {
        _logger.Verbose(message, args);
    }

    /// <summary>
    /// 记录调试信息
    /// </summary>
    public void LogDebug(string message, params object[] args)
    {
        _logger.Debug(message, args);
    }

    /// <summary>
    /// 记录一般信息
    /// </summary>
    public void LogInformation(string message, params object[] args)
    {
        _logger.Information(message, args);
    }

    /// <summary>
    /// 记录警告信息
    /// </summary>
    public void LogWarning(string message, params object[] args)
    {
        _logger.Warning(message, args);
    }

    /// <summary>
    /// 记录错误信息
    /// </summary>
    public void LogError(string message, params object[] args)
    {
        _logger.Error(message, args);
    }

    /// <summary>
    /// 记录错误信息（带异常）
    /// </summary>
    public void LogError(Exception exception, string message, params object[] args)
    {
        _logger.Error(exception, message, args);
    }

    /// <summary>
    /// 记录致命错误
    /// </summary>
    public void LogFatal(string message, params object[] args)
    {
        _logger.Fatal(message, args);
    }

    /// <summary>
    /// 记录致命错误（带异常）
    /// </summary>
    public void LogFatal(Exception exception, string message, params object[] args)
    {
        _logger.Fatal(exception, message, args);
    }

    /// <summary>
    /// 开始性能计时
    /// </summary>
    public IDisposable BeginScope(string operationName)
    {
        return new PerformanceScope(_logger, operationName);
    }

    /// <summary>
    /// 刷新日志缓冲区
    /// </summary>
    public void Flush()
    {
        Log.CloseAndFlush();
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            Flush();
            _disposed = true;
        }
    }
}

/// <summary>
/// 性能计时范围
/// </summary>
internal class PerformanceScope : IDisposable
{
    private readonly ILogger _logger;
    private readonly string _operationName;
    private readonly Stopwatch _stopwatch;
    private bool _disposed = false;

    public PerformanceScope(ILogger logger, string operationName)
    {
        _logger = logger;
        _operationName = operationName;
        _stopwatch = Stopwatch.StartNew();
        _logger.Debug("开始执行操作: {OperationName}", _operationName);
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _stopwatch.Stop();
            _logger.Debug("操作完成: {OperationName}，耗时: {ElapsedMilliseconds}ms", 
                _operationName, _stopwatch.ElapsedMilliseconds);
            _disposed = true;
        }
    }
}
