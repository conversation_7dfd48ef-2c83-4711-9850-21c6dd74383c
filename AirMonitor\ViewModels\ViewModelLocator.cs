using System.Windows;
using AirMonitor.Services;

namespace AirMonitor.ViewModels;

/// <summary>
/// ViewModel定位器，负责为View自动绑定对应的ViewModel
/// </summary>
public static class ViewModelLocator
{
    /// <summary>
    /// AutoWireViewModel附加属性
    /// </summary>
    public static readonly DependencyProperty AutoWireViewModelProperty =
        DependencyProperty.RegisterAttached(
            "AutoWireViewModel",
            typeof(bool),
            typeof(ViewModelLocator),
            new PropertyMetadata(false, OnAutoWireViewModelChanged));

    /// <summary>
    /// 获取AutoWireViewModel属性值
    /// </summary>
    /// <param name="obj">依赖对象</param>
    /// <returns>是否自动绑定ViewModel</returns>
    public static bool GetAutoWireViewModel(DependencyObject obj)
    {
        return (bool)obj.GetValue(AutoWireViewModelProperty);
    }

    /// <summary>
    /// 设置AutoWireViewModel属性值
    /// </summary>
    /// <param name="obj">依赖对象</param>
    /// <param name="value">是否自动绑定ViewModel</param>
    public static void SetAutoWireViewModel(DependencyObject obj, bool value)
    {
        obj.SetValue(AutoWireViewModelProperty, value);
    }

    /// <summary>
    /// AutoWireViewModel属性更改时的回调
    /// </summary>
    /// <param name="d">依赖对象</param>
    /// <param name="e">事件参数</param>
    private static void OnAutoWireViewModelChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if ((bool)e.NewValue)
        {
            AutoWireViewModelForView(d);
        }
    }

    /// <summary>
    /// 为视图自动绑定ViewModel
    /// </summary>
    /// <param name="view">视图对象</param>
    private static void AutoWireViewModelForView(DependencyObject view)
    {
        try
        {
            var viewType = view.GetType();
            var viewModelType = GetViewModelTypeForView(viewType);

            if (viewModelType != null)
            {
                var viewModel = CreateViewModel(viewModelType);
                if (viewModel != null)
                {
                    if (view is FrameworkElement frameworkElement)
                    {
                        frameworkElement.DataContext = viewModel;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            // 记录错误但不抛出异常，避免影响UI加载
            System.Diagnostics.Debug.WriteLine($"自动绑定ViewModel失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 根据View类型获取对应的ViewModel类型
    /// </summary>
    /// <param name="viewType">View类型</param>
    /// <returns>ViewModel类型</returns>
    private static Type? GetViewModelTypeForView(Type viewType)
    {
        // 命名约定：
        // MainWindow -> MainViewModel
        // UserControl -> UserControlViewModel
        // LoginView -> LoginViewModel
        // SettingsPage -> SettingsPageViewModel

        var viewName = viewType.Name;
        var viewModelName = GetViewModelNameFromViewName(viewName);
        
        if (string.IsNullOrEmpty(viewModelName))
        {
            return null;
        }

        // 在同一程序集中查找ViewModel
        var viewModelTypeName = $"{viewType.Namespace?.Replace(".Views", ".ViewModels")}.{viewModelName}";
        var viewModelType = viewType.Assembly.GetType(viewModelTypeName);

        // 如果在Views命名空间中没找到，尝试在ViewModels命名空间中查找
        if (viewModelType == null)
        {
            var baseNamespace = viewType.Namespace?.Split('.')[0];
            viewModelTypeName = $"{baseNamespace}.ViewModels.{viewModelName}";
            viewModelType = viewType.Assembly.GetType(viewModelTypeName);
        }

        return viewModelType;
    }

    /// <summary>
    /// 根据View名称获取ViewModel名称
    /// </summary>
    /// <param name="viewName">View名称</param>
    /// <returns>ViewModel名称</returns>
    private static string GetViewModelNameFromViewName(string viewName)
    {
        // 移除常见的View后缀
        var suffixesToRemove = new[] { "Window", "Page", "View", "Control", "UC" };
        
        foreach (var suffix in suffixesToRemove)
        {
            if (viewName.EndsWith(suffix))
            {
                var baseName = viewName.Substring(0, viewName.Length - suffix.Length);
                return $"{baseName}ViewModel";
            }
        }

        // 如果没有匹配的后缀，直接添加ViewModel
        return $"{viewName}ViewModel";
    }

    /// <summary>
    /// 创建ViewModel实例
    /// </summary>
    /// <param name="viewModelType">ViewModel类型</param>
    /// <returns>ViewModel实例</returns>
    private static object? CreateViewModel(Type viewModelType)
    {
        try
        {
            // 首先尝试从依赖注入容器获取
            var viewModel = ServiceContainer.GetServiceOrNull(viewModelType);
            if (viewModel != null)
            {
                return viewModel;
            }

            // 如果容器中没有注册，尝试使用反射创建
            return Activator.CreateInstance(viewModelType);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"创建ViewModel失败: {viewModelType.Name}, 错误: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 手动为视图绑定ViewModel
    /// </summary>
    /// <typeparam name="TViewModel">ViewModel类型</typeparam>
    /// <param name="view">视图</param>
    /// <param name="viewModel">ViewModel实例</param>
    public static void BindViewModel<TViewModel>(FrameworkElement view, TViewModel viewModel)
        where TViewModel : class
    {
        if (view == null) throw new ArgumentNullException(nameof(view));
        if (viewModel == null) throw new ArgumentNullException(nameof(viewModel));

        view.DataContext = viewModel;
    }

    /// <summary>
    /// 手动为视图绑定ViewModel（从容器获取）
    /// </summary>
    /// <typeparam name="TViewModel">ViewModel类型</typeparam>
    /// <param name="view">视图</param>
    public static void BindViewModel<TViewModel>(FrameworkElement view)
        where TViewModel : class
    {
        if (view == null) throw new ArgumentNullException(nameof(view));

        var viewModel = ServiceContainer.GetService<TViewModel>();
        view.DataContext = viewModel;
    }
}
