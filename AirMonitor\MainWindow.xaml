﻿<Window
    x:Class="AirMonitor.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:AirMonitor.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:AirMonitor"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:views="clr-namespace:AirMonitor.Views"
    xmlns:vm="clr-namespace:AirMonitor.ViewModels"
    Title="{Binding Title, FallbackValue='AirMonitor'}"
    Width="900"
    Height="600"
    vm:ViewModelLocator.AutoWireViewModel="True"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">

    <Window.Resources>
        <Style TargetType="Button">
            <Setter Property="Margin" Value="5" />
            <Setter Property="Padding" Value="10,5" />
            <Setter Property="MinWidth" Value="100" />
        </Style>
        <Style TargetType="TextBlock">
            <Setter Property="Margin" Value="5" />
            <Setter Property="VerticalAlignment" Value="Center" />
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  标题区域  -->
        <StackPanel
            Grid.Row="0"
            Margin="0,0,0,20"
            Orientation="Vertical">
            <TextBlock
                HorizontalAlignment="Center"
                FontSize="24"
                FontWeight="Bold"
                Text="{Binding WelcomeMessage}" />
            <TextBlock
                HorizontalAlignment="Center"
                FontSize="12"
                Foreground="Gray"
                Text="{Binding ApplicationVersion, StringFormat='版本: {0}'}" />
        </StackPanel>

        <!--  主内容区域  -->
        <Border
            Grid.Row="1"
            Padding="20"
            BorderBrush="LightGray"
            BorderThickness="1"
            CornerRadius="5">
            <StackPanel>
                <TextBlock
                    Margin="0,0,0,15"
                    FontSize="16"
                    FontWeight="SemiBold"
                    Text="应用程序功能演示" />

                <TextBlock Margin="0,5" Text="✓ 依赖注入系统 (Microsoft.Extensions.DependencyInjection)" />
                <TextBlock Margin="0,5" Text="✓ MVVM架构 (CommunityToolkit.Mvvm)" />
                <TextBlock Margin="0,5" Text="✓ 日志系统 (Serilog)" />
                <TextBlock Margin="0,5" Text="✓ 配置管理 (Microsoft.Extensions.Configuration)" />
                <TextBlock Margin="0,5" Text="✓ 视图模型自动绑定" />

                <Separator Margin="0,15" />

                <TextBlock FontWeight="SemiBold" Text="当前状态:" />
                <TextBlock
                    FontWeight="Medium"
                    Foreground="Green"
                    Text="{Binding StatusMessage}" />

                <Separator Margin="0,15" />

                <!-- 对话框服务演示 -->
                <TextBlock
                    Margin="0,0,0,10"
                    FontWeight="SemiBold"
                    Text="对话框服务演示:" />
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
                    <Button Command="{Binding ShowSampleDialogCommand}" Content="示例对话框" Margin="5"/>
                    <Button Command="{Binding ShowSimpleDialogCommand}" Content="简单对话框" Margin="5"/>
                    <Button Command="{Binding ShowNonModalWindowCommand}" Content="非模态窗口" Margin="5"/>
                </StackPanel>

                <Separator Margin="0,15" />

                <!--  示例用户控件演示  -->
                <TextBlock
                    Margin="0,0,0,10"
                    FontWeight="SemiBold"
                    Text="ViewModelLocator自动绑定演示:" />
                <views:SampleView HorizontalAlignment="Center" />
            </StackPanel>
        </Border>

        <!--  操作按钮区域  -->
        <StackPanel
            Grid.Row="2"
            Margin="0,20,0,0"
            HorizontalAlignment="Center"
            Orientation="Horizontal">
            <Button
                Command="{Binding InitializeCommand}"
                Content="初始化"
                IsEnabled="{Binding IsBusy, Converter={StaticResource InverseBooleanConverter}}" />
            <Button Command="{Binding AboutCommand}" Content="关于" />
            <Button Command="{Binding ExitCommand}" Content="退出" />
        </StackPanel>

        <!--  状态栏  -->
        <StatusBar Grid.Row="3" Margin="0,10,0,0">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}" />
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="忙状态: " />
                    <TextBlock Foreground="{Binding IsBusy, Converter={StaticResource BooleanToBrushConverter}}" Text="{Binding IsBusy}" />
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
