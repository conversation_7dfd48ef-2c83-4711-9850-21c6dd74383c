using System.IO;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using AirMonitor.Models;

namespace AirMonitor.Services;

/// <summary>
/// 配置服务实现
/// </summary>
public class ConfigurationService : IConfigurationService
{
    private readonly ILogger<ConfigurationService> _logger;
    private readonly string _configFilePath;
    private IConfiguration? _configuration;
    private AppSettings _appSettings;

    public AppSettings AppSettings => _appSettings;

    public event EventHandler<ConfigurationChangedEventArgs>? ConfigurationChanged;

    public ConfigurationService(ILogger<ConfigurationService> logger)
    {
        _logger = logger;
        _configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "appsettings.json");
        _appSettings = new AppSettings();
    }

    /// <summary>
    /// 加载配置
    /// </summary>
    public async Task<bool> LoadConfigurationAsync()
    {
        try
        {
            // 确保配置文件存在
            await EnsureConfigurationFileExistsAsync();

            // 构建配置
            var builder = new ConfigurationBuilder()
                .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

            _configuration = builder.Build();

            // 绑定配置到模型
            _configuration.Bind(_appSettings);

            _logger.LogInformation("配置加载成功");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载配置失败");
            return false;
        }
    }

    /// <summary>
    /// 保存配置
    /// </summary>
    public async Task<bool> SaveConfigurationAsync()
    {
        try
        {
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };

            var json = JsonSerializer.Serialize(_appSettings, options);
            await File.WriteAllTextAsync(_configFilePath, json);

            _logger.LogInformation("配置保存成功");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存配置失败");
            return false;
        }
    }

    /// <summary>
    /// 重新加载配置
    /// </summary>
    public async Task<bool> ReloadConfigurationAsync()
    {
        return await LoadConfigurationAsync();
    }

    /// <summary>
    /// 获取配置值
    /// </summary>
    public T GetValue<T>(string key, T defaultValue) where T : notnull
    {
        try
        {
            if (_configuration == null)
                return defaultValue;

            return _configuration.GetValue(key, defaultValue);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取配置值失败，键: {Key}", key);
            return defaultValue;
        }
    }

    /// <summary>
    /// 设置配置值
    /// </summary>
    public void SetValue<T>(string key, T value) where T : notnull
    {
        try
        {
            var oldValue = GetValue(key, value);

            // 这里简化处理，实际应用中可能需要更复杂的逻辑来更新嵌套配置
            // 触发配置更改事件
            ConfigurationChanged?.Invoke(this, new ConfigurationChangedEventArgs(key, oldValue, value));

            _logger.LogInformation("配置值已更新，键: {Key}", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置配置值失败，键: {Key}", key);
        }
    }

    /// <summary>
    /// 确保配置文件存在
    /// </summary>
    private async Task EnsureConfigurationFileExistsAsync()
    {
        if (!File.Exists(_configFilePath))
        {
            _logger.LogInformation("配置文件不存在，创建默认配置文件");
            await SaveConfigurationAsync();
        }
    }
}
