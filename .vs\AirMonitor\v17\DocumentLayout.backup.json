{"Version": 1, "WorkspaceRootPath": "D:\\00 AirMonitor\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\views\\sampledialog.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\views\\sampledialog.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\views\\sampledialog.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\views\\sampledialog.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\views\\sampleview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\views\\sampleview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\controls\\modaloverlay.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\controls\\modaloverlay.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\controls\\modaloverlay.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\controls\\modaloverlay.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 11, "Children": [{"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:134:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:135:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d84ee353-0bef-5a41-a649-8f89aca5d84d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "SampleView.xaml", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\Views\\SampleView.xaml", "RelativeDocumentMoniker": "AirMonitor\\Views\\SampleView.xaml", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\Views\\SampleView.xaml", "RelativeToolTip": "AirMonitor\\Views\\SampleView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-24T01:56:19.821Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "ModalOverlay.xaml.cs", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\Controls\\ModalOverlay.xaml.cs", "RelativeDocumentMoniker": "AirMonitor\\Controls\\ModalOverlay.xaml.cs", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\Controls\\ModalOverlay.xaml.cs", "RelativeToolTip": "AirMonitor\\Controls\\ModalOverlay.xaml.cs", "ViewState": "AgIAACIAAAAAAAAAAAAuwDAAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T01:54:41.833Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "ModalOverlay.xaml", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\Controls\\ModalOverlay.xaml", "RelativeDocumentMoniker": "AirMonitor\\Controls\\ModalOverlay.xaml", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\Controls\\ModalOverlay.xaml", "RelativeToolTip": "AirMonitor\\Controls\\ModalOverlay.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-24T01:54:07.875Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "SampleDialog.xaml.cs", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\Views\\SampleDialog.xaml.cs", "RelativeDocumentMoniker": "AirMonitor\\Views\\SampleDialog.xaml.cs", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\Views\\SampleDialog.xaml.cs", "RelativeToolTip": "AirMonitor\\Views\\SampleDialog.xaml.cs", "ViewState": "AgIAAFEAAAAAAAAAAAAvwHIAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T01:50:40.851Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "SampleDialog.xaml", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\Views\\SampleDialog.xaml", "RelativeDocumentMoniker": "AirMonitor\\Views\\SampleDialog.xaml", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\Views\\SampleDialog.xaml", "RelativeToolTip": "AirMonitor\\Views\\SampleDialog.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-24T01:50:25.341Z", "EditorCaption": ""}]}]}]}