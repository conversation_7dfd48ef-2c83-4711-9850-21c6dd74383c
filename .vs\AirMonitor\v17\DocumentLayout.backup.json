{"Version": 1, "WorkspaceRootPath": "D:\\00 AirMonitor\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\viewmodels\\sampledialogviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\viewmodels\\sampledialogviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\views\\sampledialog.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\views\\sampledialog.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\services\\servicecontainer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\services\\servicecontainer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\services\\dialogservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\services\\dialogservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\views\\dialogwindowbase.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\views\\dialogwindowbase.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\viewmodels\\mainviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\viewmodels\\mainviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\viewmodels\\sampleviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\viewmodels\\sampleviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\controls\\modaloverlay.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\controls\\modaloverlay.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\services\\idialogservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\services\\idialogservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\services\\config\\configurationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\services\\config\\configurationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\viewmodels\\viewmodellocator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\viewmodels\\viewmodellocator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\views\\sampleview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\views\\sampleview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\helpers\\commandhelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\helpers\\commandhelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\viewmodels\\viewmodelbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\viewmodels\\viewmodelbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|d:\\00 airmonitor\\airmonitor\\services\\config\\iconfigurationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4496C365-ED66-47CF-8050-4B2C36231928}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\services\\config\\iconfigurationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 9, "Children": [{"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:134:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:135:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d84ee353-0bef-5a41-a649-8f89aca5d84d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "SampleDialog.xaml", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\Views\\SampleDialog.xaml", "RelativeDocumentMoniker": "AirMonitor\\Views\\SampleDialog.xaml", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\Views\\SampleDialog.xaml", "RelativeToolTip": "AirMonitor\\Views\\SampleDialog.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-24T01:36:40.703Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "SampleDialogViewModel.cs", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\ViewModels\\SampleDialogViewModel.cs", "RelativeDocumentMoniker": "AirMonitor\\ViewModels\\SampleDialogViewModel.cs", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\ViewModels\\SampleDialogViewModel.cs", "RelativeToolTip": "AirMonitor\\ViewModels\\SampleDialogViewModel.cs", "ViewState": "AgIAADgAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T01:36:15.683Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "ServiceContainer.cs", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\Services\\ServiceContainer.cs", "RelativeDocumentMoniker": "AirMonitor\\Services\\ServiceContainer.cs", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\Services\\ServiceContainer.cs", "RelativeToolTip": "AirMonitor\\Services\\ServiceContainer.cs", "ViewState": "AgIAADcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T01:35:32.131Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "DialogService.cs", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\Services\\DialogService.cs", "RelativeDocumentMoniker": "AirMonitor\\Services\\DialogService.cs", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\Services\\DialogService.cs", "RelativeToolTip": "AirMonitor\\Services\\DialogService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T01:35:25.62Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "DialogWindowBase.xaml", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\Views\\DialogWindowBase.xaml", "RelativeDocumentMoniker": "AirMonitor\\Views\\DialogWindowBase.xaml", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\Views\\DialogWindowBase.xaml", "RelativeToolTip": "AirMonitor\\Views\\DialogWindowBase.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-24T01:33:52.905Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "ModalOverlay.xaml", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\Controls\\ModalOverlay.xaml", "RelativeDocumentMoniker": "AirMonitor\\Controls\\ModalOverlay.xaml", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\Controls\\ModalOverlay.xaml", "RelativeToolTip": "AirMonitor\\Controls\\ModalOverlay.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-24T01:32:01.429Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "IDialogService.cs", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\Services\\IDialogService.cs", "RelativeDocumentMoniker": "AirMonitor\\Services\\IDialogService.cs", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\Services\\IDialogService.cs", "RelativeToolTip": "AirMonitor\\Services\\IDialogService.cs", "ViewState": "AgIAAFEAAAAAAAAAAAAkwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T01:31:19.083Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "ViewModelLocator.cs", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\ViewModels\\ViewModelLocator.cs", "RelativeDocumentMoniker": "AirMonitor\\ViewModels\\ViewModelLocator.cs", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\ViewModels\\ViewModelLocator.cs", "RelativeToolTip": "AirMonitor\\ViewModels\\ViewModelLocator.cs", "ViewState": "AgIAAJwAAAAAAAAAAAAqwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T01:27:57.395Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "MainViewModel.cs", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\ViewModels\\MainViewModel.cs", "RelativeDocumentMoniker": "AirMonitor\\ViewModels\\MainViewModel.cs", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\ViewModels\\MainViewModel.cs", "RelativeToolTip": "AirMonitor\\ViewModels\\MainViewModel.cs", "ViewState": "AgIAAFsAAAAAAAAAAAAgwCsAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T01:18:33.289Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "ConfigurationService.cs", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\Services\\Config\\ConfigurationService.cs", "RelativeDocumentMoniker": "AirMonitor\\Services\\Config\\ConfigurationService.cs", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\Services\\Config\\ConfigurationService.cs", "RelativeToolTip": "AirMonitor\\Services\\Config\\ConfigurationService.cs", "ViewState": "AgIAAFEAAAAAAAAAAAAAAGIAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T01:18:09.034Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "SampleViewModel.cs", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\ViewModels\\SampleViewModel.cs", "RelativeDocumentMoniker": "AirMonitor\\ViewModels\\SampleViewModel.cs", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\ViewModels\\SampleViewModel.cs", "RelativeToolTip": "AirMonitor\\ViewModels\\SampleViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T01:19:55.733Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "appsettings.json", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\appsettings.json", "RelativeDocumentMoniker": "AirMonitor\\appsettings.json", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\appsettings.json", "RelativeToolTip": "AirMonitor\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-24T01:25:06.168Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "App.xaml.cs", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\App.xaml.cs", "RelativeDocumentMoniker": "AirMonitor\\App.xaml.cs", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\App.xaml.cs", "RelativeToolTip": "AirMonitor\\App.xaml.cs", "ViewState": "AgIAAEQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T01:25:13.601Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "App.xaml", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\App.xaml", "RelativeDocumentMoniker": "AirMonitor\\App.xaml", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\App.xaml", "RelativeToolTip": "AirMonitor\\App.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-24T01:24:33.683Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "SampleView.xaml", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\Views\\SampleView.xaml", "RelativeDocumentMoniker": "AirMonitor\\Views\\SampleView.xaml", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\Views\\SampleView.xaml", "RelativeToolTip": "AirMonitor\\Views\\SampleView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-24T01:22:04.642Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "MainWindow.xaml", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\MainWindow.xaml", "RelativeDocumentMoniker": "AirMonitor\\MainWindow.xaml", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\MainWindow.xaml", "RelativeToolTip": "AirMonitor\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-24T01:16:35.762Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "ViewModelBase.cs", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\ViewModels\\ViewModelBase.cs", "RelativeDocumentMoniker": "AirMonitor\\ViewModels\\ViewModelBase.cs", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\ViewModels\\ViewModelBase.cs", "RelativeToolTip": "AirMonitor\\ViewModels\\ViewModelBase.cs", "ViewState": "AgIAAAkAAAAAAAAAAADwvxIAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T01:19:04.009Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "CommandHelper.cs", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\Helpers\\CommandHelper.cs", "RelativeDocumentMoniker": "AirMonitor\\Helpers\\CommandHelper.cs", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\Helpers\\CommandHelper.cs", "RelativeToolTip": "AirMonitor\\Helpers\\CommandHelper.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T01:19:17.889Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "IConfigurationService.cs", "DocumentMoniker": "D:\\00 AirMonitor\\AirMonitor\\Services\\Config\\IConfigurationService.cs", "RelativeDocumentMoniker": "AirMonitor\\Services\\Config\\IConfigurationService.cs", "ToolTip": "D:\\00 AirMonitor\\AirMonitor\\Services\\Config\\IConfigurationService.cs", "RelativeToolTip": "AirMonitor\\Services\\Config\\IConfigurationService.cs", "ViewState": "AgIAACIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T01:18:18.745Z", "EditorCaption": ""}]}]}]}