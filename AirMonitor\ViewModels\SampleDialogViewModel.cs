using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.ComponentModel.DataAnnotations;

namespace AirMonitor.ViewModels;

/// <summary>
/// 示例对话框视图模型
/// </summary>
public partial class SampleDialogViewModel : ObservableValidator
{
    #region 字段

    [ObservableProperty]
    [NotifyDataErrorInfo]
    [Required(ErrorMessage = "用户名不能为空")]
    [MinLength(3, ErrorMessage = "用户名至少需要3个字符")]
    private string _userName = string.Empty;

    [ObservableProperty]
    [NotifyDataErrorInfo]
    [Required(ErrorMessage = "邮箱不能为空")]
    [EmailAddress(ErrorMessage = "请输入有效的邮箱地址")]
    private string _email = string.Empty;

    [ObservableProperty]
    private string _description = string.Empty;

    [ObservableProperty]
    private bool _isAgreed = false;

    [ObservableProperty]
    private string _selectedOption = "选项1";

    #endregion

    #region 属性

    /// <summary>
    /// 可选项列表
    /// </summary>
    public List<string> AvailableOptions { get; } = new()
    {
        "选项1",
        "选项2", 
        "选项3",
        "选项4"
    };

    /// <summary>
    /// 对话框标题
    /// </summary>
    public string DialogTitle => "示例对话框";

    /// <summary>
    /// 是否可以确定
    /// </summary>
    public bool CanConfirm => !HasErrors && IsAgreed && !string.IsNullOrWhiteSpace(UserName) && !string.IsNullOrWhiteSpace(Email);

    #endregion

    #region 构造函数

    /// <summary>
    /// 初始化 SampleDialogViewModel 类的新实例
    /// </summary>
    public SampleDialogViewModel()
    {
        // 监听属性变化以更新命令状态
        PropertyChanged += (s, e) =>
        {
            if (e.PropertyName == nameof(UserName) || 
                e.PropertyName == nameof(Email) || 
                e.PropertyName == nameof(IsAgreed) ||
                e.PropertyName == nameof(HasErrors))
            {
                ConfirmCommand.NotifyCanExecuteChanged();
            }
        };
    }

    #endregion

    #region 命令

    /// <summary>
    /// 确认命令
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanConfirm))]
    private void Confirm()
    {
        // 验证数据
        ValidateAllProperties();
        
        if (HasErrors)
        {
            return;
        }

        // 这里可以处理确认逻辑
        // 在实际使用中，对话框的关闭由DialogWindowBase处理
    }

    /// <summary>
    /// 取消命令
    /// </summary>
    [RelayCommand]
    private void Cancel()
    {
        // 取消操作，清理数据
        UserName = string.Empty;
        Email = string.Empty;
        Description = string.Empty;
        IsAgreed = false;
        SelectedOption = AvailableOptions.First();
    }

    /// <summary>
    /// 重置命令
    /// </summary>
    [RelayCommand]
    private void Reset()
    {
        UserName = string.Empty;
        Email = string.Empty;
        Description = string.Empty;
        IsAgreed = false;
        SelectedOption = AvailableOptions.First();
        ClearErrors();
    }

    #endregion

    #region 公共方法

    /// <summary>
    /// 获取表单数据
    /// </summary>
    /// <returns>表单数据字典</returns>
    public Dictionary<string, object> GetFormData()
    {
        return new Dictionary<string, object>
        {
            { nameof(UserName), UserName },
            { nameof(Email), Email },
            { nameof(Description), Description },
            { nameof(IsAgreed), IsAgreed },
            { nameof(SelectedOption), SelectedOption }
        };
    }

    /// <summary>
    /// 设置表单数据
    /// </summary>
    /// <param name="data">表单数据字典</param>
    public void SetFormData(Dictionary<string, object> data)
    {
        if (data.TryGetValue(nameof(UserName), out var userName))
            UserName = userName?.ToString() ?? string.Empty;

        if (data.TryGetValue(nameof(Email), out var email))
            Email = email?.ToString() ?? string.Empty;

        if (data.TryGetValue(nameof(Description), out var description))
            Description = description?.ToString() ?? string.Empty;

        if (data.TryGetValue(nameof(IsAgreed), out var isAgreed) && isAgreed is bool agreed)
            IsAgreed = agreed;

        if (data.TryGetValue(nameof(SelectedOption), out var selectedOption))
            SelectedOption = selectedOption?.ToString() ?? AvailableOptions.First();
    }

    #endregion
}
