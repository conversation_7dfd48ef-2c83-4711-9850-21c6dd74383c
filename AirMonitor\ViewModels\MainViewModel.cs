using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using AirMonitor.Services;
using AirMonitor.Views;

namespace AirMonitor.ViewModels;

/// <summary>
/// 主窗口ViewModel
/// </summary>
public partial class MainViewModel : ViewModelBase
{
    private readonly IConfigurationService _configurationService;
    private readonly IDialogService _dialogService;

    [ObservableProperty]
    private string _welcomeMessage = "欢迎使用 AirMonitor";

    [ObservableProperty]
    private string _statusMessage = "就绪";

    [ObservableProperty]
    private string _applicationVersion = "1.0.0";

    /// <summary>
    /// 初始化命令
    /// </summary>
    public ICommand InitializeCommand { get; }

    /// <summary>
    /// 退出命令
    /// </summary>
    public ICommand ExitCommand { get; }

    /// <summary>
    /// 关于命令
    /// </summary>
    public ICommand AboutCommand { get; }

    /// <summary>
    /// 显示示例对话框命令
    /// </summary>
    public ICommand ShowSampleDialogCommand { get; }

    /// <summary>
    /// 显示简单对话框命令
    /// </summary>
    public ICommand ShowSimpleDialogCommand { get; }

    /// <summary>
    /// 显示非模态窗口命令
    /// </summary>
    public ICommand ShowNonModalWindowCommand { get; }

    public MainViewModel(IConfigurationService configurationService, ILoggingService loggingService, IDialogService dialogService)
        : base(loggingService)
    {
        _configurationService = configurationService ?? throw new ArgumentNullException(nameof(configurationService));
        _dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));

        Title = "AirMonitor - 主窗口";

        // 初始化命令
        InitializeCommand = new AsyncRelayCommand(InitializeApplicationAsync);
        ExitCommand = new RelayCommand(Exit);
        AboutCommand = new RelayCommand(ShowAbout);
        ShowSampleDialogCommand = new RelayCommand(ShowSampleDialog);
        ShowSimpleDialogCommand = new RelayCommand(ShowSimpleDialog);
        ShowNonModalWindowCommand = new RelayCommand(ShowNonModalWindow);
    }

    /// <summary>
    /// 初始化
    /// </summary>
    protected override async Task OnInitializeAsync()
    {
        await ExecuteAsync(async () =>
        {
            LoggingService?.LogInformation("正在初始化主窗口...");

            // 加载配置
            await _configurationService.LoadConfigurationAsync();
            
            // 更新应用程序信息
            ApplicationVersion = _configurationService.AppSettings.Application.Version;
            WelcomeMessage = $"欢迎使用 {_configurationService.AppSettings.Application.Name}";
            
            StatusMessage = "初始化完成";
            
            LoggingService?.LogInformation("主窗口初始化完成");
        });
    }

    /// <summary>
    /// 初始化异步操作
    /// </summary>
    private async Task InitializeApplicationAsync()
    {
        await InitializeAsync();
    }

    /// <summary>
    /// 退出应用程序
    /// </summary>
    private void Exit()
    {
        LoggingService?.LogInformation("用户请求退出应用程序");
        StatusMessage = "正在退出...";
        
        // 这里可以添加退出前的清理逻辑
        System.Windows.Application.Current.Shutdown();
    }

    /// <summary>
    /// 显示关于信息
    /// </summary>
    private void ShowAbout()
    {
        LoggingService?.LogInformation("显示关于信息");
        
        var aboutMessage = $"""
            {_configurationService.AppSettings.Application.Name}
            版本: {ApplicationVersion}
            
            这是一个基于WPF和MVVM模式的应用程序框架示例。
            
            功能特性:
            • 依赖注入 (Microsoft.Extensions.DependencyInjection)
            • MVVM架构 (CommunityToolkit.Mvvm)
            • 日志系统 (Serilog)
            • 配置管理 (Microsoft.Extensions.Configuration)
            • 视图模型自动绑定
            """;

        System.Windows.MessageBox.Show(
            aboutMessage,
            "关于",
            System.Windows.MessageBoxButton.OK,
            System.Windows.MessageBoxImage.Information);
    }

    /// <summary>
    /// 显示示例对话框
    /// </summary>
    private void ShowSampleDialog()
    {
        try
        {
            LoggingService?.LogInformation("显示示例对话框");
            StatusMessage = "正在显示示例对话框...";

            // 创建示例对话框
            var dialog = new SampleDialog();

            // 设置初始数据（可选）
            dialog.SetInitialData(new Dictionary<string, object>
            {
                { "UserName", "示例用户" },
                { "Email", "<EMAIL>" },
                { "Description", "这是一个示例描述" },
                { "SelectedOption", "选项2" }
            });

            // 显示模态对话框
            var result = _dialogService.ShowDialog(dialog);

            if (result == true)
            {
                // 获取对话框结果
                var resultData = dialog.GetResultData();
                if (resultData != null)
                {
                    var message = "对话框返回的数据:\n\n";
                    foreach (var kvp in resultData)
                    {
                        message += $"{kvp.Key}: {kvp.Value}\n";
                    }

                    System.Windows.MessageBox.Show(message, "对话框结果",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Information);
                }
                StatusMessage = "示例对话框已确认";
            }
            else
            {
                StatusMessage = "示例对话框已取消";
            }
        }
        catch (Exception ex)
        {
            LoggingService?.LogError(ex, "显示示例对话框时发生错误");
            StatusMessage = "显示对话框失败";
            System.Windows.MessageBox.Show($"显示对话框时发生错误:\n{ex.Message}", "错误",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 显示简单对话框
    /// </summary>
    private void ShowSimpleDialog()
    {
        try
        {
            LoggingService?.LogInformation("显示简单对话框");
            StatusMessage = "正在显示简单对话框...";

            // 使用泛型方法显示对话框
            var result = _dialogService.ShowDialog<SampleDialog>();

            StatusMessage = result == true ? "简单对话框已确认" : "简单对话框已取消";
        }
        catch (Exception ex)
        {
            LoggingService?.LogError(ex, "显示简单对话框时发生错误");
            StatusMessage = "显示对话框失败";
            System.Windows.MessageBox.Show($"显示对话框时发生错误:\n{ex.Message}", "错误",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 显示非模态窗口
    /// </summary>
    private void ShowNonModalWindow()
    {
        try
        {
            LoggingService?.LogInformation("显示非模态窗口");
            StatusMessage = "正在显示非模态窗口...";

            // 显示非模态窗口
            var window = _dialogService.ShowWindow<SampleDialog>();
            window.Title = "非模态示例窗口";

            StatusMessage = "非模态窗口已显示";
        }
        catch (Exception ex)
        {
            LoggingService?.LogError(ex, "显示非模态窗口时发生错误");
            StatusMessage = "显示窗口失败";
            System.Windows.MessageBox.Show($"显示窗口时发生错误:\n{ex.Message}", "错误",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 处理错误
    /// </summary>
    protected override async Task OnErrorAsync(Exception exception, string operationName)
    {
        await base.OnErrorAsync(exception, operationName);
        
        StatusMessage = $"操作失败: {operationName}";
        
        // 显示错误消息给用户
        System.Windows.MessageBox.Show(
            $"操作 '{operationName}' 执行失败:\n{exception.Message}",
            "错误",
            System.Windows.MessageBoxButton.OK,
            System.Windows.MessageBoxImage.Error);
    }
}
