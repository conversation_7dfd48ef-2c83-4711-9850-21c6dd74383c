using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using AirMonitor.Services;

namespace AirMonitor.ViewModels;

/// <summary>
/// 主窗口ViewModel
/// </summary>
public partial class MainViewModel : ViewModelBase
{
    private readonly IConfigurationService _configurationService;

    [ObservableProperty]
    private string _welcomeMessage = "欢迎使用 AirMonitor";

    [ObservableProperty]
    private string _statusMessage = "就绪";

    [ObservableProperty]
    private string _applicationVersion = "1.0.0";

    /// <summary>
    /// 初始化命令
    /// </summary>
    public ICommand InitializeCommand { get; }

    /// <summary>
    /// 退出命令
    /// </summary>
    public ICommand ExitCommand { get; }

    /// <summary>
    /// 关于命令
    /// </summary>
    public ICommand AboutCommand { get; }

    public MainViewModel(IConfigurationService configurationService, ILoggingService loggingService)
        : base(loggingService)
    {
        _configurationService = configurationService ?? throw new ArgumentNullException(nameof(configurationService));
        
        Title = "AirMonitor - 主窗口";

        // 初始化命令
        InitializeCommand = new AsyncRelayCommand(InitializeApplicationAsync);
        ExitCommand = new RelayCommand(Exit);
        AboutCommand = new RelayCommand(ShowAbout);
    }

    /// <summary>
    /// 初始化
    /// </summary>
    protected override async Task OnInitializeAsync()
    {
        await ExecuteAsync(async () =>
        {
            LoggingService?.LogInformation("正在初始化主窗口...");

            // 加载配置
            await _configurationService.LoadConfigurationAsync();
            
            // 更新应用程序信息
            ApplicationVersion = _configurationService.AppSettings.Application.Version;
            WelcomeMessage = $"欢迎使用 {_configurationService.AppSettings.Application.Name}";
            
            StatusMessage = "初始化完成";
            
            LoggingService?.LogInformation("主窗口初始化完成");
        });
    }

    /// <summary>
    /// 初始化异步操作
    /// </summary>
    private async Task InitializeApplicationAsync()
    {
        await InitializeAsync();
    }

    /// <summary>
    /// 退出应用程序
    /// </summary>
    private void Exit()
    {
        LoggingService?.LogInformation("用户请求退出应用程序");
        StatusMessage = "正在退出...";
        
        // 这里可以添加退出前的清理逻辑
        System.Windows.Application.Current.Shutdown();
    }

    /// <summary>
    /// 显示关于信息
    /// </summary>
    private void ShowAbout()
    {
        LoggingService?.LogInformation("显示关于信息");
        
        var aboutMessage = $"""
            {_configurationService.AppSettings.Application.Name}
            版本: {ApplicationVersion}
            
            这是一个基于WPF和MVVM模式的应用程序框架示例。
            
            功能特性:
            • 依赖注入 (Microsoft.Extensions.DependencyInjection)
            • MVVM架构 (CommunityToolkit.Mvvm)
            • 日志系统 (Serilog)
            • 配置管理 (Microsoft.Extensions.Configuration)
            • 视图模型自动绑定
            """;

        System.Windows.MessageBox.Show(
            aboutMessage,
            "关于",
            System.Windows.MessageBoxButton.OK,
            System.Windows.MessageBoxImage.Information);
    }

    /// <summary>
    /// 处理错误
    /// </summary>
    protected override async Task OnErrorAsync(Exception exception, string operationName)
    {
        await base.OnErrorAsync(exception, operationName);
        
        StatusMessage = $"操作失败: {operationName}";
        
        // 显示错误消息给用户
        System.Windows.MessageBox.Show(
            $"操作 '{operationName}' 执行失败:\n{exception.Message}",
            "错误",
            System.Windows.MessageBoxButton.OK,
            System.Windows.MessageBoxImage.Error);
    }
}
