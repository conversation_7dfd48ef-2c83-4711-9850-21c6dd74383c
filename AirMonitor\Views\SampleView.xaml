<UserControl x:Class="AirMonitor.Views.SampleView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:vm="clr-namespace:AirMonitor.ViewModels"
             mc:Ignorable="d" 
             vm:ViewModelLocator.AutoWireViewModel="True"
             d:DesignHeight="300" d:DesignWidth="400">
    
    <Border BorderBrush="LightBlue" 
            BorderThickness="2" 
            CornerRadius="5" 
            Padding="15">
        <StackPanel>
            <TextBlock Text="示例用户控件" 
                       FontSize="16" 
                       FontWeight="Bold" 
                       HorizontalAlignment="Center" 
                       Margin="0,0,0,10"/>
            
            <TextBlock Text="{Binding Title}" 
                       FontSize="14" 
                       HorizontalAlignment="Center" 
                       Margin="0,0,0,10"/>
            
            <TextBlock Text="{Binding Message}" 
                       HorizontalAlignment="Center" 
                       Margin="0,0,0,15"/>
            
            <Button Content="测试命令" 
                    Command="{Binding TestCommand}" 
                    HorizontalAlignment="Center" 
                    Padding="10,5" 
                    MinWidth="100"/>
        </StackPanel>
    </Border>
</UserControl>
