using System.Collections.Concurrent;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using AirMonitor.Controls;
using Microsoft.Extensions.Logging;

namespace AirMonitor.Services;

/// <summary>
/// 对话框服务实现，提供模态和非模态窗口显示功能
/// </summary>
public class DialogService : IDialogService
{
    #region 字段

    private readonly ILogger<DialogService> _logger;
    private readonly ConcurrentDictionary<Window, ModalOverlay> _activeOverlays;
    private readonly ConcurrentDictionary<Window, List<Window>> _activeDialogs;

    #endregion

    #region 构造函数

    /// <summary>
    /// 初始化 DialogService 类的新实例
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public DialogService(ILogger<DialogService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _activeOverlays = new ConcurrentDictionary<Window, ModalOverlay>();
        _activeDialogs = new ConcurrentDictionary<Window, List<Window>>();
    }

    #endregion

    #region IDialogService 实现

    /// <summary>
    /// 显示模态对话框，带有半透明蒙版效果
    /// </summary>
    public bool? ShowDialog(Window dialog, Window? owner = null)
    {
        if (dialog == null)
            throw new ArgumentNullException(nameof(dialog));

        try
        {
            owner ??= GetActiveWindow();
            if (owner == null)
            {
                _logger.LogWarning("无法找到父窗口，将以无父窗口模式显示对话框");
                return dialog.ShowDialog();
            }

            return ShowDialogWithOverlay(dialog, owner);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "显示模态对话框时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 显示模态对话框，带有半透明蒙版效果
    /// </summary>
    public bool? ShowDialog<T>(Window? owner = null) where T : Window, new()
    {
        var dialog = new T();
        return ShowDialog(dialog, owner);
    }

    /// <summary>
    /// 显示模态对话框，带有半透明蒙版效果
    /// </summary>
    public bool? ShowDialog<T>(Func<T> dialogFactory, Window? owner = null) where T : Window
    {
        if (dialogFactory == null)
            throw new ArgumentNullException(nameof(dialogFactory));

        var dialog = dialogFactory();
        return ShowDialog(dialog, owner);
    }

    /// <summary>
    /// 显示非模态窗口，不添加蒙版效果
    /// </summary>
    public void ShowWindow(Window window, Window? owner = null)
    {
        if (window == null)
            throw new ArgumentNullException(nameof(window));

        try
        {
            owner ??= GetActiveWindow();
            if (owner != null)
            {
                window.Owner = owner;
            }

            window.Show();
            _logger.LogDebug("显示非模态窗口: {WindowType}", window.GetType().Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "显示非模态窗口时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 显示非模态窗口，不添加蒙版效果
    /// </summary>
    public T ShowWindow<T>(Window? owner = null) where T : Window, new()
    {
        var window = new T();
        ShowWindow(window, owner);
        return window;
    }

    /// <summary>
    /// 显示非模态窗口，不添加蒙版效果
    /// </summary>
    public T ShowWindow<T>(Func<T> windowFactory, Window? owner = null) where T : Window
    {
        if (windowFactory == null)
            throw new ArgumentNullException(nameof(windowFactory));

        var window = windowFactory();
        ShowWindow(window, owner);
        return window;
    }

    /// <summary>
    /// 异步显示模态对话框，带有半透明蒙版效果
    /// </summary>
    public async Task<bool?> ShowDialogAsync(Window dialog, Window? owner = null)
    {
        return await Task.Run(() => ShowDialog(dialog, owner));
    }

    /// <summary>
    /// 异步显示模态对话框，带有半透明蒙版效果
    /// </summary>
    public async Task<bool?> ShowDialogAsync<T>(Window? owner = null) where T : Window, new()
    {
        return await Task.Run(() => ShowDialog<T>(owner));
    }

    /// <summary>
    /// 异步显示模态对话框，带有半透明蒙版效果
    /// </summary>
    public async Task<bool?> ShowDialogAsync<T>(Func<T> dialogFactory, Window? owner = null) where T : Window
    {
        return await Task.Run(() => ShowDialog(dialogFactory, owner));
    }

    /// <summary>
    /// 获取当前活动的窗口
    /// </summary>
    public Window? GetActiveWindow()
    {
        return Application.Current?.Windows.OfType<Window>()
            .FirstOrDefault(w => w.IsActive) ?? Application.Current?.MainWindow;
    }

    /// <summary>
    /// 检查指定窗口是否有活动的模态对话框
    /// </summary>
    public bool HasActiveDialog(Window owner)
    {
        if (owner == null)
            return false;

        return _activeDialogs.ContainsKey(owner) && _activeDialogs[owner].Count > 0;
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 显示带蒙版的模态对话框
    /// </summary>
    private bool? ShowDialogWithOverlay(Window dialog, Window owner)
    {
        ModalOverlay? overlay = null;
        bool? result = null;

        try
        {
            // 创建并显示蒙版
            overlay = CreateOverlay(owner);
            ShowOverlay(overlay, owner);

            // 记录活动对话框
            AddActiveDialog(owner, dialog);

            // 设置对话框属性
            dialog.Owner = owner;
            dialog.WindowStartupLocation = WindowStartupLocation.CenterOwner;

            // 监听对话框关闭事件
            dialog.Closed += (s, e) =>
            {
                RemoveActiveDialog(owner, dialog);
                HideOverlay(overlay, owner);
            };

            // 显示对话框
            result = dialog.ShowDialog();

            _logger.LogDebug("模态对话框显示完成: {DialogType}, 结果: {Result}",
                dialog.GetType().Name, result);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "显示带蒙版的模态对话框时发生错误");

            // 清理资源
            if (overlay != null)
            {
                RemoveActiveDialog(owner, dialog);
                HideOverlay(overlay, owner);
            }

            throw;
        }
    }

    /// <summary>
    /// 创建蒙版控件
    /// </summary>
    private ModalOverlay CreateOverlay(Window owner)
    {
        var overlay = new ModalOverlay
        {
            IsAnimationEnabled = true,
            // 设置蒙版填充整个容器
            HorizontalAlignment = HorizontalAlignment.Stretch,
            VerticalAlignment = VerticalAlignment.Stretch,
            // 清除固定大小，让它自动填充
            Width = double.NaN,
            Height = double.NaN
        };

        return overlay;
    }

    /// <summary>
    /// 显示蒙版
    /// </summary>
    private void ShowOverlay(ModalOverlay overlay, Window owner)
    {
        if (owner.Content is Panel panel)
        {
            // 将蒙版添加到父窗口的内容面板中
            panel.Children.Add(overlay);
            Panel.SetZIndex(overlay, int.MaxValue);

            _activeOverlays[owner] = overlay;
            overlay.Show();

            _logger.LogDebug("蒙版已显示在窗口: {WindowType}", owner.GetType().Name);
        }
        else if (owner.Content is ContentControl contentControl)
        {
            // 如果父窗口内容是ContentControl，需要特殊处理
            var grid = new Grid();
            var originalContent = contentControl.Content;

            contentControl.Content = grid;
            grid.Children.Add(originalContent as UIElement);
            grid.Children.Add(overlay);

            Panel.SetZIndex(overlay, int.MaxValue);

            _activeOverlays[owner] = overlay;
            overlay.Show();

            _logger.LogDebug("蒙版已显示在ContentControl窗口: {WindowType}", owner.GetType().Name);
        }
        else
        {
            _logger.LogWarning("无法在窗口上显示蒙版，窗口内容类型不支持: {ContentType}",
                owner.Content?.GetType().Name ?? "null");
        }
    }

    /// <summary>
    /// 隐藏蒙版
    /// </summary>
    private void HideOverlay(ModalOverlay overlay, Window owner)
    {
        try
        {
            if (_activeOverlays.TryRemove(owner, out var activeOverlay) && activeOverlay == overlay)
            {
                // 使用Dispatcher确保在UI线程上执行
                owner.Dispatcher.BeginInvoke(new Action(() =>
                {
                    if (overlay.Parent is Panel panel)
                    {
                        panel.Children.Remove(overlay);
                    }
                    else if (owner.Content is ContentControl contentControl &&
                             contentControl.Content is Grid grid &&
                             grid.Children.Contains(overlay))
                    {
                        grid.Children.Remove(overlay);

                        // 恢复原始内容
                        if (grid.Children.Count == 1)
                        {
                            var originalContent = grid.Children[0];
                            grid.Children.Clear();
                            contentControl.Content = originalContent;
                        }
                    }

                    overlay.Hide();
                }), DispatcherPriority.Normal);

                _logger.LogDebug("蒙版已从窗口移除: {WindowType}", owner.GetType().Name);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "隐藏蒙版时发生错误");
        }
    }

    /// <summary>
    /// 添加活动对话框记录
    /// </summary>
    private void AddActiveDialog(Window owner, Window dialog)
    {
        _activeDialogs.AddOrUpdate(owner,
            new List<Window> { dialog },
            (key, list) =>
            {
                list.Add(dialog);
                return list;
            });
    }

    /// <summary>
    /// 移除活动对话框记录
    /// </summary>
    private void RemoveActiveDialog(Window owner, Window dialog)
    {
        if (_activeDialogs.TryGetValue(owner, out var dialogs))
        {
            dialogs.Remove(dialog);
            if (dialogs.Count == 0)
            {
                _activeDialogs.TryRemove(owner, out _);
            }
        }
    }

    #endregion
}
