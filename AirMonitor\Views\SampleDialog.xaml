<local:DialogWindowBase x:Class="AirMonitor.Views.SampleDialog"
                       xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                       xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                       xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                       xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                       xmlns:local="clr-namespace:AirMonitor.Views"
                       xmlns:vm="clr-namespace:AirMonitor.ViewModels"
                       mc:Ignorable="d"
                       Title="{Binding DialogTitle}"
                       DialogTitle="{Binding DialogTitle}"
                       Height="500" 
                       Width="450"
                       MinHeight="400"
                       MinWidth="400"
                       d:DataContext="{d:DesignInstance Type=vm:SampleDialogViewModel}">

    <local:DialogWindowBase.Resources>
        <!-- 输入框样式 -->
        <Style x:Key="InputTextBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="{DynamicResource {x:Static SystemColors.ControlDarkBrushKey}}"/>
            <Style.Triggers>
                <Trigger Property="Validation.HasError" Value="True">
                    <Setter Property="BorderBrush" Value="Red"/>
                    <Setter Property="BorderThickness" Value="2"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 标签样式 -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="Margin" Value="0,0,0,5"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.WindowTextBrushKey}}"/>
        </Style>

        <!-- 错误信息样式 -->
        <Style x:Key="ErrorTextStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="Red"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Margin" Value="0,-8,0,8"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>
    </local:DialogWindowBase.Resources>

    <!-- 对话框内容 -->
    <local:DialogWindowBase.DialogContent>
        <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
            <StackPanel Margin="0,0,0,10">
                
                <!-- 用户名输入 -->
                <TextBlock Text="用户名 *" Style="{StaticResource LabelStyle}"/>
                <TextBox Text="{Binding UserName, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
                         Style="{StaticResource InputTextBoxStyle}"/>
                <TextBlock Text="{Binding (Validation.Errors)[0].ErrorContent, RelativeSource={RelativeSource PreviousData}}"
                           Style="{StaticResource ErrorTextStyle}"
                           Visibility="{Binding (Validation.HasError), RelativeSource={RelativeSource PreviousData}, Converter={x:Static BooleanToVisibilityConverter.Instance}}"/>

                <!-- 邮箱输入 -->
                <TextBlock Text="邮箱地址 *" Style="{StaticResource LabelStyle}"/>
                <TextBox Text="{Binding Email, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
                         Style="{StaticResource InputTextBoxStyle}"/>
                <TextBlock Text="{Binding (Validation.Errors)[0].ErrorContent, RelativeSource={RelativeSource PreviousData}}"
                           Style="{StaticResource ErrorTextStyle}"
                           Visibility="{Binding (Validation.HasError), RelativeSource={RelativeSource PreviousData}, Converter={x:Static BooleanToVisibilityConverter.Instance}}"/>

                <!-- 选项选择 -->
                <TextBlock Text="选择选项" Style="{StaticResource LabelStyle}"/>
                <ComboBox ItemsSource="{Binding AvailableOptions}"
                          SelectedItem="{Binding SelectedOption}"
                          Margin="0,0,0,15"
                          Padding="8,6"/>

                <!-- 描述输入 -->
                <TextBlock Text="描述信息" Style="{StaticResource LabelStyle}"/>
                <TextBox Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource InputTextBoxStyle}"
                         Height="80"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         VerticalScrollBarVisibility="Auto"/>

                <!-- 同意条款 -->
                <CheckBox IsChecked="{Binding IsAgreed}"
                          Margin="0,10,0,0">
                    <TextBlock Text="我同意相关条款和条件 *" TextWrapping="Wrap"/>
                </CheckBox>

                <!-- 操作按钮 -->
                <StackPanel Orientation="Horizontal" 
                           HorizontalAlignment="Right" 
                           Margin="0,20,0,0">
                    <Button Content="重置" 
                           Command="{Binding ResetCommand}"
                           Margin="0,0,10,0"
                           Padding="15,6"
                           MinWidth="80"/>
                </StackPanel>

            </StackPanel>
        </ScrollViewer>
    </local:DialogWindowBase.DialogContent>

</local:DialogWindowBase>
