<Window
    x:Class="AirMonitor.Views.SampleDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:AirMonitor.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:AirMonitor.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:AirMonitor.ViewModels"
    Title="{Binding DialogTitle}"
    Width="450"
    Height="500"
    MinWidth="400"
    MinHeight="400"
    d:DataContext="{d:DesignInstance Type=vm:SampleDialogViewModel}"
    ResizeMode="CanResize"
    ShowInTaskbar="False"
    WindowStartupLocation="CenterOwner"
    mc:Ignorable="d">

    <Window.Resources>


        <!--  输入框样式  -->
        <Style x:Key="InputTextBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="8,6" />
            <Setter Property="Margin" Value="0,0,0,10" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="BorderBrush" Value="{DynamicResource {x:Static SystemColors.ControlDarkBrushKey}}" />
            <Style.Triggers>
                <Trigger Property="Validation.HasError" Value="True">
                    <Setter Property="BorderBrush" Value="Red" />
                    <Setter Property="BorderThickness" Value="2" />
                </Trigger>
            </Style.Triggers>
        </Style>

        <!--  标签样式  -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="Margin" Value="0,0,0,5" />
            <Setter Property="FontWeight" Value="SemiBold" />
            <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.WindowTextBrushKey}}" />
        </Style>

        <!--  错误信息样式  -->
        <Style x:Key="ErrorTextStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="Red" />
            <Setter Property="FontSize" Value="11" />
            <Setter Property="Margin" Value="0,-8,0,8" />
            <Setter Property="TextWrapping" Value="Wrap" />
        </Style>

        <!--  按钮样式  -->
        <Style x:Key="DialogButtonStyle" TargetType="Button">
            <Setter Property="MinWidth" Value="75" />
            <Setter Property="MinHeight" Value="23" />
            <Setter Property="Margin" Value="3" />
            <Setter Property="Padding" Value="8,2" />
        </Style>
    </Window.Resources>

    <Border Padding="20">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!--  标题  -->
            <TextBlock
                Grid.Row="0"
                Margin="0,0,0,15"
                FontSize="16"
                FontWeight="SemiBold"
                Text="{Binding DialogTitle}" />

            <!--  内容区域  -->
            <ScrollViewer
                Grid.Row="1"
                HorizontalScrollBarVisibility="Disabled"
                VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="0,0,0,10">

                    <!--  用户名输入  -->
                    <TextBlock Style="{StaticResource LabelStyle}" Text="用户名 *" />
                    <TextBox Style="{StaticResource InputTextBoxStyle}" Text="{Binding UserName, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}" />

                    <!--  邮箱输入  -->
                    <TextBlock Style="{StaticResource LabelStyle}" Text="邮箱地址 *" />
                    <TextBox Style="{StaticResource InputTextBoxStyle}" Text="{Binding Email, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}" />

                    <!--  选项选择  -->
                    <TextBlock Style="{StaticResource LabelStyle}" Text="选择选项" />
                    <ComboBox
                        Margin="0,0,0,15"
                        Padding="8,6"
                        ItemsSource="{Binding AvailableOptions}"
                        SelectedItem="{Binding SelectedOption}" />

                    <!--  描述输入  -->
                    <TextBlock Style="{StaticResource LabelStyle}" Text="描述信息" />
                    <TextBox
                        Height="80"
                        AcceptsReturn="True"
                        Style="{StaticResource InputTextBoxStyle}"
                        Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                        TextWrapping="Wrap"
                        VerticalScrollBarVisibility="Auto" />

                    <!--  同意条款  -->
                    <CheckBox Margin="0,10,0,0" IsChecked="{Binding IsAgreed}">
                        <TextBlock Text="我同意相关条款和条件 *" TextWrapping="Wrap" />
                    </CheckBox>

                    <!--  操作按钮  -->
                    <StackPanel
                        Margin="0,20,0,0"
                        HorizontalAlignment="Right"
                        Orientation="Horizontal">
                        <Button
                            Margin="0,0,10,0"
                            Command="{Binding ResetCommand}"
                            Content="重置"
                            Style="{StaticResource DialogButtonStyle}" />
                    </StackPanel>

                </StackPanel>
            </ScrollViewer>

            <!--  对话框按钮  -->
            <StackPanel
                Grid.Row="2"
                Margin="0,15,0,0"
                HorizontalAlignment="Right"
                Orientation="Horizontal">
                <Button
                    x:Name="OkButton"
                    Click="OkButton_Click"
                    Content="确定"
                    IsDefault="True"
                    Style="{StaticResource DialogButtonStyle}" />
                <Button
                    x:Name="CancelButton"
                    Click="CancelButton_Click"
                    Content="取消"
                    IsCancel="True"
                    Style="{StaticResource DialogButtonStyle}" />
            </StackPanel>
        </Grid>
    </Border>
</Window>
